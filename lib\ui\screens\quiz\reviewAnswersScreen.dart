import 'dart:developer';

import 'package:flutter/cupertino.dart';
import 'package:flutter/material.dart';
import 'package:flutter_bloc/flutter_bloc.dart';
import 'package:flutterquiz/features/bookmark/bookmarkRepository.dart';
import 'package:flutterquiz/features/bookmark/cubits/bookmarkCubit.dart';
import 'package:flutterquiz/features/bookmark/cubits/updateBookmarkCubit.dart';
import 'package:flutterquiz/features/profileManagement/cubits/userDetailsCubit.dart';
import 'package:flutterquiz/features/quiz/models/answerOption.dart';
import 'package:flutterquiz/features/quiz/models/question.dart';
import 'package:flutterquiz/features/quiz/models/quizType.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionCubit.dart';
import 'package:flutterquiz/features/reportQuestion/reportQuestionRepository.dart';
import 'package:flutterquiz/features/systemConfig/cubits/systemConfigCubit.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/explanation_dialog.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/questionContainer.dart';
import 'package:flutterquiz/ui/screens/quiz/widgets/report_question_bottom_sheet.dart';
import 'package:flutterquiz/ui/widgets/alreadyLoggedInDialog.dart';
import 'package:flutterquiz/utils/answer_encryption.dart';
import 'package:flutterquiz/utils/constants/error_message_keys.dart';
import 'package:flutterquiz/utils/constants/string_labels.dart';
import 'package:flutterquiz/utils/extensions.dart';
import 'package:flutterquiz/utils/ui_utils.dart';
import 'package:google_fonts/google_fonts.dart';
import '../../../features/profileManagement/cubits/updateScoreAndCoinsCubit.dart';
import '../../../features/profileManagement/profileManagementRepository.dart';

class ReviewAnswersScreen extends StatefulWidget {
  const ReviewAnswersScreen({
    required this.questions,
    required this.quizType,
    super.key,
  });

  final List<Question> questions;
  final QuizTypes quizType;

  static Route<dynamic> route(RouteSettings routeSettings) {
    final arguments = routeSettings.arguments as Map?;
    //arguments will map and keys of the map are following
    //questions
    return CupertinoPageRoute(
      builder: (_) => MultiBlocProvider(
        providers: [
          BlocProvider<UpdateBookmarkCubit>(
            create: (context) => UpdateBookmarkCubit(BookmarkRepository()),
          ),
          BlocProvider<ReportQuestionCubit>(
            create: (_) => ReportQuestionCubit(ReportQuestionRepository()),
          ),
          // Add UpdateScoreAndCoinsCubit provider
          BlocProvider<UpdateScoreAndCoinsCubit>(
            create: (_) =>
                UpdateScoreAndCoinsCubit(ProfileManagementRepository()),
          ),
        ],
        child: ReviewAnswersScreen(
          quizType: arguments!['quizType'] as QuizTypes,
          questions: arguments['questions'] as List<Question>? ?? <Question>[],
        ),
      ),
    );
  }

  @override
  State<ReviewAnswersScreen> createState() => _ReviewAnswersScreenState();
}

class _ReviewAnswersScreenState extends State<ReviewAnswersScreen> {
  late final _pageController = PageController();
  int _currQueIdx = 0;

  late final _firebaseId = context.read<UserDetailsCubit>().getUserFirebaseId();

  // تم إزالة متغير _isAudioQuestions لأن الأسئلة الصوتية لم تعد مدعومة

  late final questionsLength = widget.questions.length;

  // تم إزالة متغير _musicPlayerKeys لأنه لم يعد مطلوبًا بعد إزالة الأسئلة الصوتية
  late final _correctAnswerIds = List.generate(
    widget.questions.length,
    (i) => AnswerEncryption.decryptCorrectAnswer(
      rawKey: _firebaseId,
      correctAnswer: widget.questions[i].correctAnswer!,
    ),
    growable: false,
  );

  bool get isLatex {
    return context.read<SystemConfigCubit>().isLatexModeEnabled &&
        switch (widget.quizType) {
          QuizTypes.quizZone => true,
          QuizTypes.dailyQuiz => true,
          QuizTypes.selfChallenge => true,
          QuizTypes.exam => true,

          // تم إزالة الإشارة إلى الأسئلة الصوتية
          _ => false,
        };
  }

  void _showExplanationDialog() {
    if (!_hasExplanation()) return;

    final currentQuestion = widget.questions[_currQueIdx];
    showDialog(
      context: context,
      builder: (BuildContext context) {
        return ExplanationDialog(
          question: currentQuestion,
          // لا نحتاج لإيقاف المؤقت في صفحة المراجعة لأنه لا يوجد مؤقت
          onDialogOpened: null,
          onDialogClosed: null,
        );
      },
    );
  }

  void _onTapReportQuestion() {
    showReportQuestionBottomSheet(
      context: context,
      questionId: widget.questions[_currQueIdx].id!,
      reportQuestionCubit: context.read<ReportQuestionCubit>(),
    );
  }

  void _onPageChanged(int idx) {
    // تم إزالة منطق تشغيل الصوت لأن الأسئلة الصوتية لم تعد مدعومة
    setState(() => _currQueIdx = idx);
  }

  Color _optionBackgroundColor(String? optionId) {
    final correctAnswerId = _correctAnswerIds[_currQueIdx];
    final submittedAnswerId = widget.questions[_currQueIdx].submittedAnswerId;

    // الإجابة الصحيحة - لون أخضر معتدل
    if (optionId == correctAnswerId) {
      return Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFF388E3C) // أخضر معتدل للوضع المظلم
          : const Color(0xFF66BB6A); // أخضر فاتح معتدل للوضع العادي
    }

    // الإجابة المختارة الخاطئة - لون أحمر معتدل
    if (optionId == submittedAnswerId && optionId != correctAnswerId) {
      return Theme.of(context).brightness == Brightness.dark
          ? const Color(0xFFD32F2F) // أحمر معتدل للوضع المظلم
          : const Color(0xFFEF5350); // أحمر فاتح معتدل للوضع العادي
    }

    // الخيارات الأخرى - لون رمادي فاتح
    return Theme.of(context).brightness == Brightness.dark
        ? const Color(0xFF424242) // رمادي غامق للوضع المظلم
        : const Color(0xFFF5F5F5); // رمادي فاتح للوضع العادي
  }

  Color _optionTextColor(String? optionId) {
    final correctAnswerId = _correctAnswerIds[_currQueIdx];
    final submittedAnswerId = widget.questions[_currQueIdx].submittedAnswerId;

    // نص أبيض للإجابات الصحيحة والخاطئة (لأن الخلفية ملونة)
    if (optionId == correctAnswerId ||
        (optionId == submittedAnswerId && optionId != correctAnswerId)) {
      return Colors.white;
    }

    // نص أسود أو رمادي للخيارات الأخرى
    return Theme.of(context).brightness == Brightness.dark
        ? Colors.white70
        : Colors.black87;
  }

  /// التحقق من وجود شرح للسؤال الحالي
  bool _hasExplanation() {
    if (widget.questions.isEmpty) return false;

    final currentQuestion = widget.questions[_currQueIdx];
    final hasNote =
        currentQuestion.note != null && currentQuestion.note!.isNotEmpty;
    final hasVideo = currentQuestion.hasVideo;

    return hasNote || hasVideo;
  }

  Widget _buildOptions() => Column(
        children: widget.questions[_currQueIdx].answerOptions!
            .map(_buildOption)
            .toList(),
      );

  Widget _buildQuestionAndOptions(Question question, int index) {
    return SingleChildScrollView(
      child: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          QuestionContainer(
            isMathQuestion: isLatex,
            question: question,
            questionColor: Theme.of(context).colorScheme.onSurface,
          ),
          const SizedBox(),
          _buildOptions(),
          const SizedBox(height: 15),
        ],
      ),
    );
  }

  // تم إزالة دالة _buildGuessTheWordQuestionAndOptions لأن أسئلة خمن الكلمة لم تعد مدعومة

  Widget _buildReportButton() {
    return _buildActionButton(
      icon: Icons.flag_rounded,
      onPressed: _onTapReportQuestion,
      tooltip: context.tr('reportQuestion') ?? 'تبليغ عن السؤال',
      color: Colors.red,
    );
  }

  Widget _buildBookmarkButton() {
    // For Quiz Zone, Self Challenge, Daily Quiz, and Fun and Learn types
    if (widget.quizType == QuizTypes.quizZone ||
        widget.quizType == QuizTypes.selfChallenge ||
        widget.quizType == QuizTypes.dailyQuiz ||
        widget.quizType == QuizTypes.funAndLearn ||
        widget.quizType == QuizTypes.bookmarkQuiz) {
      final bookmarkCubit = context.read<BookmarkCubit>();
      final updateBookmarkCubit = context.read<UpdateBookmarkCubit>();

      return BlocListener<UpdateBookmarkCubit, UpdateBookmarkState>(
        bloc: updateBookmarkCubit,
        listener: (context, state) {
          if (state is UpdateBookmarkFailure) {
            if (state.errorMessageCode == errorCodeUnauthorizedAccess) {
              showAlreadyLoggedInDialog(context);
              return;
            }

            if (state.failedStatus == '0') {
              bookmarkCubit.addBookmarkQuestion(widget.questions[_currQueIdx]);
            } else {
              bookmarkCubit.removeBookmarkQuestion(
                widget.questions[_currQueIdx].id!,
              );
            }

            UiUtils.showSnackBar(
              context.tr(
                convertErrorCodeToLanguageKey(
                  errorCodeUpdateBookmarkFailure,
                ),
              )!,
              context,
            );
          }
        },
        child: BlocBuilder<BookmarkCubit, BookmarkState>(
          bloc: bookmarkCubit,
          builder: (context, state) {
            if (state is BookmarkFetchSuccess) {
              final isBookmarked = bookmarkCubit.hasQuestionBookmarked(
                widget.questions[_currQueIdx].id!,
              );

              return _buildBookmarkButtonUI(
                isBookmarked: isBookmarked,
                onTap: () {
                  if (isBookmarked) {
                    bookmarkCubit.removeBookmarkQuestion(
                      widget.questions[_currQueIdx].id!,
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.questions[_currQueIdx].id!,
                      '0',
                      '1',
                    );
                    _showBookmarkSnackBar(false);
                  } else {
                    bookmarkCubit.addBookmarkQuestion(
                      widget.questions[_currQueIdx],
                    );
                    updateBookmarkCubit.updateBookmark(
                      widget.questions[_currQueIdx].id!,
                      '1',
                      '1',
                    );
                    _showBookmarkSnackBar(true);
                  }
                },
              );
            }

            if (state is BookmarkFetchFailure) {
              log('Bookmark Fetch Failure: ${state.errorMessageCode}');
            }
            return const SizedBox();
          },
        ),
      );
    }

    // تم إزالة الكود المتعلق بالإشارات المرجعية للأسئلة الصوتية

    // تم إزالة الكود المتعلق بأسئلة خمن الكلمة

    return const SizedBox.shrink();
  }

  // Helper method to show a beautiful snackbar for bookmark actions
  void _showBookmarkSnackBar(bool isBookmarked) {
    final message = isBookmarked
        ? 'تم حفظ السؤال في صفحة أخطائي'
        : 'تم إزالة السؤال من صفحة أخطائي';

    ScaffoldMessenger.of(context).hideCurrentSnackBar();
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Row(
          children: [
            Icon(
              isBookmarked ? Icons.bookmark_added : Icons.bookmark_remove,
              color: Theme.of(context).colorScheme.onPrimary,
            ),
            const SizedBox(width: 10),
            Text(
              message,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                fontSize: 16,
              ),
            ),
          ],
        ),
        backgroundColor: isBookmarked
            ? Theme.of(context).primaryColor
            : Theme.of(context).colorScheme.error,
        behavior: SnackBarBehavior.floating,
        margin: const EdgeInsets.all(8),
        shape: RoundedRectangleBorder(
          borderRadius: BorderRadius.circular(10),
        ),
        duration: const Duration(seconds: 2),
        action: SnackBarAction(
          label: 'حسناً',
          textColor: Theme.of(context).colorScheme.onPrimary,
          onPressed: () {
            ScaffoldMessenger.of(context).hideCurrentSnackBar();
          },
        ),
      ),
    );
  }

  // Helper method to build consistent bookmark button UI
  Widget _buildBookmarkButtonUI({
    required bool isBookmarked,
    required VoidCallback onTap,
  }) {
    return _buildActionButton(
      icon:
          isBookmarked ? CupertinoIcons.bookmark_fill : CupertinoIcons.bookmark,
      onPressed: onTap,
      tooltip: context.tr(isBookmarked ? 'removeBookmark' : 'addBookmark') ??
          (isBookmarked ? 'إزالة من أخطائي' : 'حفظ في أخطائي'),
      color: isBookmarked ? Theme.of(context).primaryColor : Colors.grey,
    );
  }

  @override
  Widget build(BuildContext context) {
    return BlocListener<UpdateScoreAndCoinsCubit, UpdateScoreAndCoinsState>(
      listener: (context, state) {
        if (state is UpdateScoreAndCoinsFailure) {
          if (state.errorMessage == errorCodeUnauthorizedAccess) {
            showAlreadyLoggedInDialog(context);
          }
        }
      },
      child: Scaffold(
        appBar: AppBar(
          backgroundColor: Colors.transparent,
          elevation: 0,
          title: Text(
            context.tr('reviewAnswerLbl')!,
            style: TextStyle(
              color: Theme.of(context).primaryColor,
              fontWeight: FontWeight.bold,
              fontSize: 20,
            ),
          ),
          centerTitle: true,
          iconTheme: IconThemeData(color: Theme.of(context).primaryColor),
        ),
        extendBodyBehindAppBar: true,
        body: Container(
          width: MediaQuery.of(context).size.width,
          height: MediaQuery.of(context).size.height,
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [
                Theme.of(context).primaryColor.withValues(alpha: 0.8),
                Theme.of(context).colorScheme.surface,
              ],
              begin: Alignment.topCenter,
              end: Alignment.bottomCenter,
              stops: const [0.2, 0.9],
            ),
          ),
          child: SafeArea(
            child: Stack(
              children: [
                // زخارف الخلفية
                Positioned(
                  top: -50,
                  right: -50,
                  child: Container(
                    width: 150,
                    height: 150,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color:
                          Theme.of(context).primaryColor.withValues(alpha: 0.2),
                    ),
                  ),
                ),

                Positioned(
                  bottom: -80,
                  left: -80,
                  child: Container(
                    width: 200,
                    height: 200,
                    decoration: BoxDecoration(
                      shape: BoxShape.circle,
                      color: Theme.of(context)
                          .primaryColor
                          .withValues(alpha: 0.15),
                    ),
                  ),
                ),

                // محتوى الأسئلة بدون حاوية مزدحمة
                Expanded(
                  child: Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      // زر الرجوع بتصميم محسن
                      Material(
                        elevation: 8,
                        borderRadius: BorderRadius.circular(15),
                        child: Container(
                          decoration: BoxDecoration(
                            color: Theme.of(context).colorScheme.surface,
                            borderRadius: BorderRadius.circular(15),
                            border: Border.all(
                              color: Theme.of(context)
                                  .primaryColor
                                  .withValues(alpha: 0.2),
                              width: 1,
                            ),
                          ),
                          child: IconButton(
                            icon: Icon(
                              Icons.arrow_back_ios_new_rounded,
                              color: Theme.of(context).primaryColor,
                              size: 22,
                            ),
                            onPressed: () {
                              Navigator.of(context).pop();
                            },
                            tooltip: 'رجوع',
                          ),
                        ),
                      ),

                      // العنوان
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 20,
                          vertical: 12,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context)
                              .colorScheme
                              .surface
                              .withValues(alpha: 0.9),
                          borderRadius: BorderRadius.circular(25),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context)
                                  .shadowColor
                                  .withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Text(
                          context.tr('reviewAnswerLbl')!,
                          style: TextStyle(
                            fontSize: 18,
                            fontWeight: FontWeight.bold,
                            color: Theme.of(context).primaryColor,
                          ),
                        ),
                      ),

                      // عداد الأسئلة
                      Container(
                        padding: const EdgeInsets.symmetric(
                          horizontal: 15.0,
                          vertical: 8.0,
                        ),
                        decoration: BoxDecoration(
                          color: Theme.of(context).colorScheme.surface,
                          borderRadius: BorderRadius.circular(20),
                          boxShadow: [
                            BoxShadow(
                              color: Theme.of(context)
                                  .shadowColor
                                  .withValues(alpha: 0.1),
                              blurRadius: 10,
                              offset: const Offset(0, 3),
                            ),
                          ],
                        ),
                        child: Text(
                          '${_currQueIdx + 1}/$questionsLength',
                          style: TextStyle(
                            color: Theme.of(context).primaryColor,
                            fontWeight: FontWeight.bold,
                            fontSize: 16,
                          ),
                        ),
                      ),
                    ],
                  ),
                ),

                // محتوى الأسئلة بدون حاوية مزدحمة
                Expanded(
                  child: Container(
                    decoration: BoxDecoration(
                      gradient: LinearGradient(
                        colors: Theme.of(context).brightness == Brightness.dark
                            ? [
                                Theme.of(context).colorScheme.surface,
                                Theme.of(context)
                                    .colorScheme
                                    .surface
                                    .withValues(alpha: 0.95),
                              ]
                            : [
                                const Color(0xFFF8FAFC),
                                const Color(0xFFE2E8F0),
                              ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                    child: Column(
                      children: [
                        // Action buttons في الأعلى
                        Material(
                          elevation: 4,
                          child: Container(
                            width: double.infinity,
                            padding: const EdgeInsets.symmetric(
                                horizontal: 20, vertical: 16),
                            decoration: BoxDecoration(
                              color: Theme.of(context).colorScheme.surface,
                              border: Border(
                                bottom: BorderSide(
                                  color: Theme.of(context)
                                      .primaryColor
                                      .withValues(alpha: 0.1),
                                  width: 1,
                                ),
                              ),
                            ),
                            child: Row(
                              mainAxisAlignment: MainAxisAlignment.end,
                              children: [
                                // زر الشرح
                                if (_hasExplanation()) ...[
                                  _buildActionButton(
                                    icon: Icons.lightbulb_outline,
                                    onPressed: _showExplanationDialog,
                                    tooltip: 'الشرح',
                                    color: Colors.orange,
                                  ),
                                  const SizedBox(width: 12),
                                ],
                                _buildBookmarkButton(),
                                const SizedBox(width: 12),
                                _buildReportButton(),
                              ],
                            ),
                          ),
                        ),

                        // PageView for questions
                        Expanded(
                          child: PageView.builder(
                            controller: _pageController,
                            onPageChanged: _onPageChanged,
                            itemCount: questionsLength,
                            physics:
                                const PageScrollPhysics(), // تحسين فيزياء التمرير
                            itemBuilder: (context, index) {
                              return SingleChildScrollView(
                                padding: const EdgeInsets.all(20),
                                physics:
                                    const BouncingScrollPhysics(), // تحسين التمرير العمودي
                                child: Column(
                                  crossAxisAlignment:
                                      CrossAxisAlignment.stretch,
                                  children: [
                                    // Question Container محسن
                                    Container(
                                      padding: const EdgeInsets.all(20),
                                      decoration: BoxDecoration(
                                        gradient: LinearGradient(
                                          colors: Theme.of(context)
                                                      .brightness ==
                                                  Brightness.dark
                                              ? [
                                                  Theme.of(context)
                                                      .colorScheme
                                                      .surface,
                                                  Theme.of(context)
                                                      .colorScheme
                                                      .surface
                                                      .withValues(alpha: 0.95),
                                                ]
                                              : [
                                                  Colors.white,
                                                  const Color(0xFFF8F9FA),
                                                ],
                                          begin: Alignment.topLeft,
                                          end: Alignment.bottomRight,
                                        ),
                                        borderRadius: BorderRadius.circular(20),
                                        border: Border.all(
                                          color: Theme.of(context).brightness ==
                                                  Brightness.dark
                                              ? Theme.of(context)
                                                  .primaryColor
                                                  .withValues(alpha: 0.2)
                                              : Theme.of(context)
                                                  .primaryColor
                                                  .withValues(alpha: 0.3),
                                          width: 1.5,
                                        ),
                                        boxShadow: [
                                          BoxShadow(
                                            color: Theme.of(context)
                                                        .brightness ==
                                                    Brightness.dark
                                                ? Theme.of(context)
                                                    .primaryColor
                                                    .withValues(alpha: 0.1)
                                                : Theme.of(context)
                                                    .primaryColor
                                                    .withValues(alpha: 0.15),
                                            blurRadius: 15,
                                            spreadRadius: 1,
                                            offset: const Offset(0, 6),
                                          ),
                                          if (Theme.of(context).brightness ==
                                              Brightness.light)
                                            BoxShadow(
                                              color: Colors.black
                                                  .withValues(alpha: 0.05),
                                              blurRadius: 10,
                                              spreadRadius: 0,
                                              offset: const Offset(0, 3),
                                            ),
                                        ],
                                      ),
                                      child: _buildQuestionAndOptions(
                                        widget.questions[index],
                                        index,
                                      ),
                                    ),

                                    const SizedBox(height: 20),

                                    // Notes Section محسن (if available)
                                    if (widget.questions[index].note
                                            ?.isNotEmpty ==
                                        true) ...[
                                      const SizedBox(height: 20),
                                      Container(
                                        padding: const EdgeInsets.all(18),
                                        decoration: BoxDecoration(
                                          gradient: LinearGradient(
                                            colors: Theme.of(context)
                                                        .brightness ==
                                                    Brightness.dark
                                                ? [
                                                    Colors.orange
                                                        .withValues(alpha: 0.1),
                                                    Colors.orange.withValues(
                                                        alpha: 0.05),
                                                  ]
                                                : [
                                                    const Color(0xFFFFF7ED),
                                                    const Color(0xFFFEF3C7),
                                                  ],
                                            begin: Alignment.topLeft,
                                            end: Alignment.bottomRight,
                                          ),
                                          borderRadius:
                                              BorderRadius.circular(16),
                                          border: Border.all(
                                            color: Colors.orange
                                                .withValues(alpha: 0.3),
                                            width: 1.5,
                                          ),
                                          boxShadow: [
                                            BoxShadow(
                                              color: Colors.orange
                                                  .withValues(alpha: 0.1),
                                              blurRadius: 8,
                                              offset: const Offset(0, 3),
                                            ),
                                          ],
                                        ),
                                        child: Column(
                                          crossAxisAlignment:
                                              CrossAxisAlignment.start,
                                          children: [
                                            Row(
                                              children: [
                                                Container(
                                                  padding:
                                                      const EdgeInsets.all(8),
                                                  decoration: BoxDecoration(
                                                    color: Colors.orange
                                                        .withValues(alpha: 0.2),
                                                    shape: BoxShape.circle,
                                                  ),
                                                  child: Icon(
                                                    Icons.lightbulb_outline,
                                                    color: Colors.orange,
                                                    size: 20,
                                                  ),
                                                ),
                                                const SizedBox(width: 12),
                                                Text(
                                                  context.tr(notesKey)!,
                                                  style: TextStyle(
                                                    fontSize: 18,
                                                    fontWeight: FontWeight.bold,
                                                    color: Colors.orange,
                                                  ),
                                                ),
                                              ],
                                            ),
                                            const SizedBox(height: 12),
                                            Text(
                                              widget.questions[index].note!,
                                              style: TextStyle(
                                                fontSize: 16,
                                                height: 1.5,
                                                color: Theme.of(context)
                                                    .colorScheme
                                                    .onSurface,
                                              ),
                                            ),
                                          ],
                                        ),
                                      ),
                                    ],

                                    // مساحة إضافية للتمرير
                                    const SizedBox(height: 100),
                                  ],
                                ),
                              );
                            },
                          ),
                        ),
                      ],
                    ),
                  ),
                ),

                // أزرار التنقل المحسنة
                Positioned(
                  bottom: 20,
                  left: 20,
                  right: 20,
                  child: Container(
                    padding: const EdgeInsets.symmetric(
                        horizontal: 16, vertical: 12),
                    decoration: BoxDecoration(
                      color: Theme.of(context).colorScheme.surface,
                      borderRadius: BorderRadius.circular(20),
                      border: Border.all(
                        color: Theme.of(context)
                            .primaryColor
                            .withValues(alpha: 0.2),
                        width: 1,
                      ),
                      boxShadow: [
                        BoxShadow(
                          color: Theme.of(context)
                              .shadowColor
                              .withValues(alpha: 0.1),
                          blurRadius: 15,
                          spreadRadius: 2,
                          offset: const Offset(0, 6),
                        ),
                      ],
                    ),
                    child: Row(
                      mainAxisAlignment: MainAxisAlignment.spaceBetween,
                      children: [
                        // زر السؤال السابق
                        if (_currQueIdx > 0)
                          _buildNavigationButton(
                            icon: Icons.arrow_back_ios_new_rounded,
                            onPressed: () {
                              _pageController.previousPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                            tooltip: 'السؤال السابق',
                          )
                        else
                          const SizedBox(width: 48),

                        // مؤشر الصفحة الحالية
                        Container(
                          padding: const EdgeInsets.symmetric(
                              horizontal: 16, vertical: 8),
                          decoration: BoxDecoration(
                            gradient: LinearGradient(
                              colors: [
                                Theme.of(context)
                                    .primaryColor
                                    .withValues(alpha: 0.1),
                                Theme.of(context)
                                    .primaryColor
                                    .withValues(alpha: 0.05),
                              ],
                              begin: Alignment.topCenter,
                              end: Alignment.bottomCenter,
                            ),
                            borderRadius: BorderRadius.circular(12),
                            border: Border.all(
                              color: Theme.of(context)
                                  .primaryColor
                                  .withValues(alpha: 0.3),
                              width: 1,
                            ),
                          ),
                          child: Text(
                            '${_currQueIdx + 1} / $questionsLength',
                            style: TextStyle(
                              fontSize: 16,
                              fontWeight: FontWeight.bold,
                              color: Theme.of(context).primaryColor,
                            ),
                          ),
                        ),

                        // زر السؤال التالي
                        if (_currQueIdx < questionsLength - 1)
                          _buildNavigationButton(
                            icon: Icons.arrow_forward_ios_rounded,
                            onPressed: () {
                              _pageController.nextPage(
                                duration: const Duration(milliseconds: 300),
                                curve: Curves.easeInOut,
                              );
                            },
                            tooltip: 'السؤال التالي',
                          )
                        else
                          const SizedBox(width: 48),
                      ],
                    ),
                  ),
                )
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildOption(AnswerOption option) {
    final backgroundColor = _optionBackgroundColor(option.id);
    final textColor = _optionTextColor(option.id);
    final correctAnswerId = _correctAnswerIds[_currQueIdx];
    final submittedAnswerId = widget.questions[_currQueIdx].submittedAnswerId;

    // تحديد نوع الخيار
    final isCorrect = option.id == correctAnswerId;
    final isWrong =
        option.id == submittedAnswerId && option.id != correctAnswerId;

    return Container(
      width: double.infinity,
      margin: const EdgeInsets.only(top: 12),
      decoration: BoxDecoration(
        color: backgroundColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: isCorrect
              ? const Color(0xFF388E3C) // أخضر معتدل للحدود
              : isWrong
                  ? const Color(0xFFD32F2F) // أحمر معتدل للحدود
                  : backgroundColor.withValues(alpha: 0.3),
          width:
              isCorrect || isWrong ? 2.0 : 1.5, // حدود معتدلة للإجابات المهمة
        ),
        boxShadow: [
          BoxShadow(
            color: isCorrect
                ? const Color(0xFF66BB6A)
                    .withValues(alpha: 0.3) // ظل أخضر معتدل
                : isWrong
                    ? const Color(0xFFEF5350)
                        .withValues(alpha: 0.3) // ظل أحمر معتدل
                    : backgroundColor.withValues(alpha: 0.2),
            blurRadius: isCorrect || isWrong ? 10 : 8, // ظل معتدل
            spreadRadius: isCorrect || isWrong ? 1 : 1, // انتشار معتدل
            offset: Offset(0, isCorrect || isWrong ? 4 : 4), // إزاحة معتدلة
          ),
          if (Theme.of(context).brightness == Brightness.light)
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.05),
              blurRadius: 6,
              spreadRadius: 0,
              offset: const Offset(0, 2),
            ),
        ],
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: () {},
          borderRadius: BorderRadius.circular(15),
          child: Padding(
            padding: const EdgeInsets.symmetric(
              horizontal: 20,
              vertical: 15,
            ),
            child: Row(
              children: [
                // أيقونة للإجابة الصحيحة أو الخاطئة
                if (isCorrect) ...[
                  Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.check_circle,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),
                  const SizedBox(width: 10),
                ] else if (isWrong) ...[
                  Container(
                    padding: const EdgeInsets.all(2),
                    decoration: BoxDecoration(
                      color: Colors.white.withValues(alpha: 0.2),
                      shape: BoxShape.circle,
                    ),
                    child: Icon(
                      Icons.cancel,
                      color: Colors.white,
                      size: 22,
                    ),
                  ),
                  const SizedBox(width: 10),
                ],

                // النص
                Expanded(
                  child: Text(
                    option.title!,
                    textAlign: TextAlign.center,
                    style: GoogleFonts.ibmPlexSansArabic(
                      color: textColor,
                      fontSize: 16,
                      fontWeight: isCorrect || isWrong
                          ? FontWeight.bold
                          : FontWeight.w500,
                    ),
                  ),
                ),

                // مساحة فارغة للتوازن إذا كانت هناك أيقونة
                if (isCorrect || isWrong) const SizedBox(width: 28),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// بناء زر الإجراءات المحسن
  Widget _buildActionButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
    required Color color,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              color.withValues(alpha: 0.1),
              color.withValues(alpha: 0.05),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: [
            BoxShadow(
              color: color.withValues(alpha: 0.2),
              blurRadius: 6,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Icon(
                icon,
                color: color,
                size: 22,
              ),
            ),
          ),
        ),
      ),
    );
  }

  /// بناء زر التنقل المحسن
  Widget _buildNavigationButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          gradient: LinearGradient(
            colors: [
              Theme.of(context).primaryColor,
              Theme.of(context).primaryColor.withValues(alpha: 0.8),
            ],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
          borderRadius: BorderRadius.circular(12),
          boxShadow: [
            BoxShadow(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.3),
              blurRadius: 8,
              offset: const Offset(0, 3),
            ),
          ],
        ),
        child: Material(
          color: Colors.transparent,
          child: InkWell(
            onTap: onPressed,
            borderRadius: BorderRadius.circular(12),
            child: Padding(
              padding: const EdgeInsets.all(12),
              child: Icon(
                icon,
                color: Colors.white,
                size: 24,
              ),
            ),
          ),
        ),
      ),
    );
  }

  @override
  void dispose() {
    super.dispose();
  }
}
